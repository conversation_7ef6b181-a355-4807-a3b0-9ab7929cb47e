
var suggestedProducts = function(products, searchWord) {
    products.sort();
  const out = [];  
  for(let i = 0; i < searchWord.length; i++){
   out[i] = []
   if(i > 0 ) products = [...out[i - 1]]
   for(let j = 0; j < products.length; j++){
    if(product[i][j] === searchWord[i]){
     out[i].push(products[j])
    }
   }
  }
    
  for (let i=0; i<out.length; i++) {
        if (out[i].length > 3) {
            out[i].length =3;
        } else {
            return out;
        }
    }
  return out
};


console.log(suggestedProducts(["mobile","mouse","moneypot","monitor","mousepad"], "mouse"))