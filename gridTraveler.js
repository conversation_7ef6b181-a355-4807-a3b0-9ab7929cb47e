const gridTraveler = (m, n, memo={}) => {
 const key = `${m},${n}`
 if(key in memo) return memo[key]
 if(m === 1 && n === 1) return 1
 if(m === 0 || n === 0) return 0

 memo[key] = gridTraveler(m - 1, n, memo) + gridTraveler(m, n - 1, memo)

 return memo[key]

}
// time complexity: 2 ^ m+n
// space complexity: n + m

//memoized

// time complexity: m * n
// space complexity: n + m

console.log(gridTraveler(2, 3))
console.log(gridTraveler(3, 2))
console.log(gridTraveler(3, 3))
console.log(gridTraveler(100, 40))
