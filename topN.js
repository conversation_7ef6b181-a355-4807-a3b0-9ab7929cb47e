// numCompetitors=6
// topNCompetitors = 2
// competitors = [newshop, shopnow, afashion, fashionbeats, mymarket, tcellular]
// numReviews = 6
// reviews = [
// "newshop is providing good services in the city; everyone should use newshop",
// "best services by newshop",
// "fashionbeats has great services in the city",
// "I am proud to have fashionbeats",
// "mymarket has awesome services",
// "Thanks Newshop for the quick delivery"]


const topNumCompetitors = (numCompetitors, topNCompetitors, competitors, numReviews, reviews) => {

 let report = {}, res = []

 for(let i of competitors) {
  report[i] = 0
 }

 for(let competitor of competitors){
  for(let r of reviews){
   if(r.split(" ").map(item => item.toLowerCase()).join(" ").search(competitor.toLowerCase()) !== -1){
    report[competitor] = report[competitor] + 1
   }
  }
 }

 for(let k of Object.keys(report)){
  if(report[k] > 0) {
   res = topNCompetitors > numCompetitors ? [...res, k].slice(0, topNCompetitors) : []
  }
 }
 // if(topNCompetitors > numCompetitors) {
 // } else if (topNCompetitors < numCompetitors) {
 //  for(let k of Object.keys(report)){
 //   if(report[k] > 0) {
 //    res = [...res, k].slice(0, topNCompetitors)
 //   }
 //  }
 // } else {

 // }
 // console.log(report)
 return res

}


console.log(topNumCompetitors(
 6,
 2, 
 ["newshop", "shopnow", "afashion", "fashionbeats", "mymarket", "tcellular"],
 6,
 [
  "newshop is providing good services in the city; everyone should use newshop",
  "best services by newshop",
  "fashionbeats has great services in the city",
  "I am proud to have fashionbeats",
  "mymarket has awesome services",
  "Thanks Newshop for the quick delivery"]
))