'use client'

import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'

export default function Home() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-xl">Loading...</div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-16">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">
          Welcome to Next.js + Supabase Auth
        </h1>
        
        <p className="text-xl text-gray-600 mb-12">
          A complete authentication system with sign up, sign in, and protected routes.
        </p>

        {user ? (
          <div className="space-y-6">
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              Welcome back, {user.email}!
            </div>
            <Link
              href="/dashboard"
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-200"
            >
              Go to Dashboard
            </Link>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
              Please sign in to access your dashboard.
            </div>
            <div className="space-x-4">
              <Link
                href="/signup"
                className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-200"
              >
                Sign Up
              </Link>
              <Link
                href="/login"
                className="inline-block bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg transition duration-200"
              >
                Sign In
              </Link>
            </div>
          </div>
        )}

        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-2">🔐 Secure Authentication</h3>
            <p className="text-gray-600">
              Built with Supabase Auth for secure user management and session handling.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-2">🛡️ Protected Routes</h3>
            <p className="text-gray-600">
              Middleware-based route protection ensures only authenticated users can access protected pages.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-2">⚡ Next.js 15</h3>
            <p className="text-gray-600">
              Built with the latest Next.js features including App Router and Server Components.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
