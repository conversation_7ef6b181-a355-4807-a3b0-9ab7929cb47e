'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'

export default function DashboardPage() {
  const { user, loading } = useAuth()
  const [userStats, setUserStats] = useState({
    joinDate: '',
    lastSignIn: '',
  })

  useEffect(() => {
    if (user) {
      setUserStats({
        joinDate: new Date(user.created_at).toLocaleDateString(),
        lastSignIn: new Date(user.last_sign_in_at || user.created_at).toLocaleDateString(),
      })
    }
  }, [user])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-xl">Loading...</div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-xl text-red-600">Access denied. Please log in.</div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-md p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Dashboard</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-blue-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-blue-900 mb-2">Welcome!</h2>
            <p className="text-blue-700">
              You are successfully authenticated and can access this protected page.
            </p>
          </div>
          
          <div className="bg-green-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-green-900 mb-2">User Info</h2>
            <p className="text-green-700">
              <strong>Email:</strong> {user.email}
            </p>
            <p className="text-green-700">
              <strong>User ID:</strong> {user.id}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Account Statistics</h3>
            <div className="space-y-2">
              <p className="text-gray-700">
                <strong>Member since:</strong> {userStats.joinDate}
              </p>
              <p className="text-gray-700">
                <strong>Last sign in:</strong> {userStats.lastSignIn}
              </p>
              <p className="text-gray-700">
                <strong>Email verified:</strong> {user.email_confirmed_at ? 'Yes' : 'No'}
              </p>
            </div>
          </div>
          
          <div className="bg-purple-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-purple-900 mb-2">Quick Actions</h3>
            <div className="space-y-3">
              <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded transition duration-200">
                Update Profile
              </button>
              <button className="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded transition duration-200">
                Change Password
              </button>
              <button className="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded transition duration-200">
                Account Settings
              </button>
            </div>
          </div>
        </div>

        <div className="mt-8 p-6 bg-yellow-50 rounded-lg">
          <h3 className="text-lg font-semibold text-yellow-900 mb-2">🎉 Congratulations!</h3>
          <p className="text-yellow-800">
            You have successfully set up Supabase authentication with Next.js. This dashboard is a protected route 
            that can only be accessed by authenticated users. The middleware automatically redirects 
            unauthenticated users to the login page.
          </p>
        </div>
      </div>
    </div>
  )
}
