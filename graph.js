const adList = new Map()

function addNode(node){
 adList.set(node, [])
}

function addEdge(node, data){
 adList.get(node).push(data)
 adList.get(data).push(node)
}

function bfs(start, target = 'JFK') {

 const visited = new Set();

 const queue = [start]


 while (queue.length > 0) {

     const airport = queue.shift(); // mutates the queue

     const destinations = adList.get(airport);


     for (const destination of destinations) {
        console.log(`dest: ${destination}`)

         if (destination === target)  {

             console.log(`${airport} -> ${target}: BFS found Bangkok!`)
             return
         }

         if (!visited.has(destination)) {
             visited.add(destination);
             queue.push(destination);
             console.log(destination)
         }
        
     }

     
 }

}

function dfs(start, visited = new Set(), count = 0) {

 console.log(start)
 
 visited.add(start);

 const destinations = adList.get(start);

 for (const destination of destinations) {

     if (destination === 'BKK') { 
         console.log(`DFS found Bangkok`, count)
         return console.log(`DFS found Bangkok`, count)
     }
     
     if (!visited.has(destination)) {
         return dfs(destination, visited, count + 1);
     }

 }

 return "can't fly there bro!"

}


const airports = 'PHX BKK OKC JFK LAX MEX EZE HEL LOS LAP LIM'.split(' ');

const routes = [
    ['PHX', 'LAX'],
    ['PHX', 'JFK'],
    ['JFK', 'OKC'],
    ['JFK', 'HEL'],
    ['JFK', 'LOS'],
    ['MEX', 'LAX'],
    ['MEX', 'BKK'],
    ['MEX', 'LIM'],
    ['MEX', 'EZE'],
    ['LIM', 'BKK'],
];


airports.forEach(addNode)
routes.forEach(log => addEdge(...log))

console.log(adList)
// console.log(dfs("LIM"))
console.log(bfs("MEX"))