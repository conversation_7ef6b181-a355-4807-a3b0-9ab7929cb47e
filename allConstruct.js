const allConstruct = (target, words, memo={}) => {

 if(target in memo) return memo[target]
 if(target === '') return [[]]

 let possibleWays = []

 for(let word of words) {

  if(target.indexOf(word) === 0) {
    const suffix = target.slice(word.length)
    const suffixWays = allConstruct(suffix, words)
    const targetWays = suffixWays.map(way => [word, ...way])
    possibleWays.push(...targetWays)
  }
    
 }

 memo[target] = possibleWays
 return possibleWays

}

console.log(allConstruct('purple', ['purp', 'p', 'ur', 'le', 'purpl']))