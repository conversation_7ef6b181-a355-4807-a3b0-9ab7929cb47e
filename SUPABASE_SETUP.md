# Supabase Authentication Setup Guide

This project includes a complete authentication system using Supabase and Next.js. Follow these steps to get it running:

## 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up/sign in
2. Click "New Project"
3. Choose your organization and enter project details
4. Wait for the project to be created (this takes a few minutes)

## 2. Get Your Supabase Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (something like `https://your-project-id.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

## 3. Configure Environment Variables

1. Open the `.env.local` file in your project root
2. Replace the placeholder values with your actual Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-actual-anon-key-here
```

## 4. Set Up Authentication in Supabase

1. In your Supabase dashboard, go to **Authentication** → **Settings**
2. Under **Site URL**, add your development URL: `http://localhost:3000`
3. Under **Redirect URLs**, add: `http://localhost:3000/auth/callback`

## 5. Enable Email Authentication

1. In **Authentication** → **Providers**
2. Make sure **Email** is enabled (it should be by default)
3. You can also enable other providers like Google, GitHub, etc. if desired

## 6. Run the Application

```bash
npm run dev
```

Visit `http://localhost:3000` to see your app!

## Features Included

- ✅ User registration (sign up)
- ✅ User login (sign in)
- ✅ User logout (sign out)
- ✅ Protected routes (dashboard)
- ✅ Automatic redirects based on auth state
- ✅ Email confirmation (if enabled in Supabase)
- ✅ Session management
- ✅ Responsive design with Tailwind CSS

## File Structure

```
src/
├── app/
│   ├── dashboard/page.tsx    # Protected dashboard page
│   ├── login/page.tsx        # Login page
│   ├── signup/page.tsx       # Sign up page
│   ├── layout.tsx            # Root layout with AuthProvider
│   └── page.tsx              # Home page
├── components/
│   ├── AuthForm.tsx          # Reusable auth form
│   └── Navbar.tsx            # Navigation component
├── contexts/
│   └── AuthContext.tsx       # Authentication context
├── lib/
│   ├── supabase.ts           # Supabase client
│   └── supabase-server.ts    # Server-side Supabase client
└── middleware.ts             # Route protection middleware
```

## Testing the Authentication

1. **Sign Up**: Go to `/signup` and create a new account
2. **Email Confirmation**: Check your email for a confirmation link (if enabled)
3. **Sign In**: Go to `/login` and sign in with your credentials
4. **Dashboard**: Access the protected `/dashboard` page
5. **Sign Out**: Use the sign out button in the navbar

## Troubleshooting

- **"Invalid API key"**: Double-check your environment variables
- **"Site URL not allowed"**: Make sure you've added `http://localhost:3000` to your Supabase site URLs
- **Email not received**: Check your spam folder, or disable email confirmation in Supabase settings for testing

## Next Steps

- Customize the UI/styling
- Add password reset functionality
- Implement user profiles
- Add social authentication providers
- Set up database tables for your app data
