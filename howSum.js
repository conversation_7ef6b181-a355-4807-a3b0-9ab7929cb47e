const howSum = (target, numbers, memo={}) => {
 
 if(target in memo) return memo[target]
 if(target === 0) return []
 if(target < 0) return null
 
 for(let num of numbers) {
  let remainders = target - num
  const remaindersResult = howSum(remainders, numbers, memo)
  if(remaindersResult){
   memo[target] = [...remaindersResult, num]
   return memo[target]
  }
 }
 memo[target] = null
 return null

}


// time complexity: n^m * m
// space complexity: m

//memoized

// time complexity: n * m^2
// space complexity: m

console.log(howSum(7, [2, 3]))
console.log(howSum(7, [5, 3, 4, 7]))
console.log(howSum(8, [2, 3, 5]))
console.log(howSum(300, [7, 14]))