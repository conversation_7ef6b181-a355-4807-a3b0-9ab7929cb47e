const bestSum = (targetSum, numbers, memo={}) => {

 if(targetSum in memo) return memo[targetSum]
 if(targetSum === 0) return []
 if(targetSum < 0) return null
 

 let shortestWay = null
 
 for(let num of numbers) {
  const remainders = targetSum - num
  const resultCombination = bestSum(remainders, numbers, memo)
  if(resultCombination) {
   if(!shortestWay || resultCombination.length < shortestWay.length) {
    shortestWay = [...resultCombination, num]
   }
  }
 }
 
 memo[targetSum] = shortestWay
 return shortestWay

}

// time complexity: n^m * m
// space complexity: m^2

//memoized

// time complexity: n * m^2
// space complexity: m^2

console.log(bestSum(7, [5, 3, 4, 7]))
console.log(bestSum(8, [1, 4, 5]))
console.log(bestSum(8, [2, 3, 5]))
console.log(bestSum(100, [1, 2, 5, 25]))