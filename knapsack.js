// const knapsackSolution = (values, weights, )

class Knapsack {

 static solve(values, weights, n, target){

  if(target < 0) return Number.MIN_SAFE_INTEGER

  if(n < 0 || target === 0) return 0
  let include = values[n] + this.solve(values, weights, n - 1, target - weights[n]),
  exclude = this.solve(values, weights, n - 1, target)
  console.log({ include, exclude })

  return Math.max(include, exclude)

 }

}

let  values = [12, 2, 1, 4, 1],
weights = [4, 2, 1, 10, 2],
target = 15

const knapsack = Knapsack.solve(
 values,
 weights,
 weights.length - 1,
 target
)

console.log(knapsack)