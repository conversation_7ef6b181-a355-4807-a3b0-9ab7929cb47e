const canSum = (target, numbers, memo={}) => {
 if(target in memo) return memo[target]
 if(target === 0) return true;
 if(target < 0) return false

 for(let num of numbers) {
  let remainders = target - num
  if(canSum(remainders, numbers, memo)) {
   memo[target] = true
   return true
  }
 }

 memo[target] = false;
 return false

}



// time complexity: n^m
// space complexity: m

//memoized 

// time complexity: n * m
// space complexity: m

console.log(canSum(7, [2, 3]))
console.log(canSum(7, [2, 3, 7, 5]))
console.log(canSum(300, [7, 14]))