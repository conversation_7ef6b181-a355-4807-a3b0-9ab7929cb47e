const canConstruct = (target, words, memo={}) => {
 if(target in memo) return memo[target]
 if(target === '') return true;

 for(let word of words) {
  
  if(target.indexOf(word) === 0) {
   const suffix = target.slice(word.length)
   if(canConstruct(suffix, words, memo)) {
    memo[target] = true
    return memo[target]
   }
  }

 }

 memo[target] = false

 return memo[target];

}

// time complexity 
// N^M * M
//space complexity
// M^2

console.log(canConstruct("abcdef", ['ab', 'abc', 'cd', 'def', 'abcd']))
console.log(canConstruct("eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeef", [
 'ee',
 'eeeeeeeeeeee',
 'eee',
 'e'
]))