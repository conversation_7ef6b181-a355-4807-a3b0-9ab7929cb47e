const countConstruct = (target, words, memo={}) => {
 
 if(target in memo) return memo[target]
 if(target === '') return 1

 let count = 0

 for(let word of words) {

  if(target.indexOf(word) === 0) {

   const suffix = target.slice(word.length)

   count += countConstruct(suffix, words, memo)

  }

 }

 memo[target] = count

 return count

}

console.log(countConstruct('purple', ['purp', 'p', 'ur', 'le', 'purpl']))
console.log(countConstruct("abcdef", ['ab', 'abc', 'cd', 'def', 'abcd']))
console.log(countConstruct("eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeef", [
 'ee',
 'eeeeeeeeeeee',
 'eee',
 'e'
]))